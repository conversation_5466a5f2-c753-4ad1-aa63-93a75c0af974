# VoIP Application Environment Configuration
# Copy this file to .env and customize for your environment

# ===========================================
# FRONTEND CONFIGURATION (React App)
# ===========================================

# Backend API URL (use hostname instead of IP for better portability)
REACT_APP_API_URL=http://localhost:8080

# Asterisk SIP Server Configuration (use hostname/service name)
REACT_APP_SIP_SERVER=asterisk.local
REACT_APP_SIP_PORT=8088
REACT_APP_SIP_WS_URL=ws://asterisk.local:8088/ws

# WebSocket URL for signaling
REACT_APP_WS_URL=ws://localhost:8080/ws

# Client IP (use localhost for development, actual IP for production)
REACT_APP_CLIENT_IP=localhost

# Debug mode
REACT_APP_DEBUG=true

# ===========================================
# ALTERNATIVE CONFIGURATIONS
# ===========================================

# For Docker deployment:
# REACT_APP_API_URL=http://voip-backend:8080
# REACT_APP_WS_URL=ws://voip-backend:8080/ws
# REACT_APP_SIP_SERVER=voip-asterisk
# REACT_APP_SIP_WS_URL=ws://voip-asterisk:8088/ws

# For production with domain names:
# REACT_APP_API_URL=https://api.yourdomain.com
# REACT_APP_WS_URL=wss://api.yourdomain.com/ws
# REACT_APP_SIP_SERVER=sip.yourdomain.com
# REACT_APP_SIP_WS_URL=wss://sip.yourdomain.com:8088/ws

# For current IP-based setup (fallback):
# REACT_APP_API_URL=http://***********:8080
# REACT_APP_WS_URL=ws://***********:8080/ws
# REACT_APP_SIP_SERVER=***********
# REACT_APP_SIP_WS_URL=ws://***********:8088/ws
# REACT_APP_CLIENT_IP=***********
