;
; Asterisk Dialplan Configuration for VoIP Application
; File: /etc/asterisk/extensions.conf
;
; This dialplan handles call routing between extensions
; for your VoIP application
;

[general]
static=yes
writeprotect=no
clearglobalvars=no

;==========================
; GLOBAL VARIABLES
;==========================

[globals]
; Define global variables for your VoIP system
CONSOLE=Console/dsp
IAXINFO=guest
TRUNK=DAHDI/G2
TRUNKMSD=1

;==========================
; DEFAULT CONTEXT
;==========================

[default]
; This context handles calls between internal extensions

; Extension pattern for 4-digit extensions (1000-1999)
exten => _1XXX,1,NoOp(Call to extension ${EXTEN})
exten => _1XXX,n,Set(CALLERID(name)=Extension ${CALLERID(num)})
exten => _1XXX,n,Dial(PJSIP/${EXTEN},30,rtT)
exten => _1XXX,n,Hangup()

; Handle busy extensions
exten => _1XXX,n(busy),Busy(20)

; Handle unavailable extensions
exten => _1XXX,n(unavail),Congestion(20)

; Voicemail access (dial *98)
exten => *98,1,NoOp(Voicemail access)
exten => *98,n,VoiceMailMain(${CALLERID(num)})
exten => *98,n,Hangup()

; Echo test (dial *43)
exten => *43,1,NoOp(Echo test)
exten => *43,n,Answer()
exten => *43,n,Wait(1)
exten => *43,n,Playback(demo-echotest)
exten => *43,n,Echo()
exten => *43,n,Hangup()

; Speaking clock (dial *60)
exten => *60,1,NoOp(Speaking clock)
exten => *60,n,Answer()
exten => *60,n,Wait(1)
exten => *60,n,SayUnixTime()
exten => *60,n,Hangup()

; Music on hold test (dial *61)
exten => *61,1,NoOp(Music on hold test)
exten => *61,n,Answer()
exten => *61,n,Wait(1)
exten => *61,n,MusicOnHold()

; Call parking (dial *70)
exten => *70,1,NoOp(Call parking)
exten => *70,n,Park()

; Pickup parked calls (*71-*79)
exten => _*7X,1,NoOp(Pickup parked call ${EXTEN:2})
exten => _*7X,n,ParkedCall(${EXTEN:2})

;==========================
; INTERNAL CONTEXT
;==========================

[internal]
; Include default context for internal calls
include => default

; Allow calls to all internal extensions
exten => _1XXX,1,NoOp(Internal call to ${EXTEN})
exten => _1XXX,n,Dial(PJSIP/${EXTEN},30,rtT)
exten => _1XXX,n,Hangup()

;==========================
; FEATURES CONTEXT
;==========================

[features]
; Feature codes for advanced functionality

; Call transfer (blind transfer)
exten => _*2XXX,1,NoOp(Blind transfer to ${EXTEN:2})
exten => _*2XXX,n,Transfer(PJSIP/${EXTEN:2})

; Conference room (dial *500)
exten => *500,1,NoOp(Conference room)
exten => *500,n,Answer()
exten => *500,n,Wait(1)
exten => *500,n,ConfBridge(1)

; Call recording toggle (dial *1)
exten => *1,1,NoOp(Toggle call recording)
exten => *1,n,Set(RECFILE=/var/spool/asterisk/monitor/${CALLERID(num)}-${STRFTIME(${EPOCH},,%Y%m%d-%H%M%S)})
exten => *1,n,Monitor(wav,${RECFILE},m)
exten => *1,n,Playback(beep)

;==========================
; HANGUP HANDLERS
;==========================

[hangup-handler]
; Handle call cleanup
exten => s,1,NoOp(Call ended - cleanup)
exten => s,n,Return()

;==========================
; ERROR HANDLING
;==========================

[invalid]
; Handle invalid extensions
exten => _X.,1,NoOp(Invalid extension: ${EXTEN})
exten => _X.,n,Answer()
exten => _X.,n,Wait(1)
exten => _X.,n,Playback(pbx-invalid)
exten => _X.,n,Hangup()

; Handle special cases
exten => i,1,NoOp(Invalid extension dialed)
exten => i,n,Playback(pbx-invalid)
exten => i,n,Hangup()

exten => t,1,NoOp(Call timeout)
exten => t,n,Hangup()

exten => h,1,NoOp(Call hangup)
exten => h,n,Return()
