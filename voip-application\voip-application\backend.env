# VoIP Backend Configuration
# Copy this file to your backend directory as .env

# Server Configuration
PORT=8080
HOST=0.0.0.0

# JWT Configuration
JWT_SECRET=voip-secret-key-change-in-production-2024
JWT_EXPIRY_HOURS=24

# Database Configuration
DB_PATH=./voip.db

# Asterisk Configuration - FIXED IP ADDRESS
ASTERISK_HOST=***********
ASTERISK_AMI_PORT=5038
ASTERISK_AMI_USERNAME=admin
ASTERISK_AMI_SECRET=amp111

# SIP Configuration
SIP_DOMAIN=***********
SIP_PORT=8088

# Public host for frontend connections
PUBLIC_HOST=***********

# Environment and service info
ENVIRONMENT=development
SERVICE_NAME=voip-backend

# CORS Configuration - Allow your PC IP
CORS_ORIGINS=http://***********:3000,http://localhost:3000,http://127.0.0.1:3000

# Service discovery
DISCOVERY_MODE=auto
DEBUG=true

# Performance optimizations
AMI_TIMEOUT=10
HTTP_TIMEOUT=5
WEBSOCKET_TIMEOUT=30
