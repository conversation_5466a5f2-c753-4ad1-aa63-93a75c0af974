# Server Configuration
PORT=8080
HOST=0.0.0.0

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRY_HOURS=24

# Database Configuration
DB_PATH=./voip.db

# Asterisk Configuration (your Asterisk server)
ASTERISK_HOST=***********
ASTERISK_AMI_PORT=5038
ASTERISK_AMI_USERNAME=admin
ASTERISK_AMI_SECRET=amp111

# SIP Configuration
SIP_DOMAIN=***********
SIP_PORT=8088

# Public host for frontend connections
PUBLIC_HOST=***********

# CORS Configuration (allow connections from your PC)
CORS_ORIGINS=http://localhost:3000,http://***********:3000,http://127.0.0.1:3000,http://***********:3000,http://***********:3000,http://***********:3000

# Debug Mode
DEBUG=true
