;
; Asterisk PJSIP Configuration for VoIP Application
; File: /etc/asterisk/pjsip.conf
;
; This configuration sets up SIP endpoints and WebSocket transport
; for your VoIP application running on *********** (frontend/backend)
; connecting to Asterisk on ***********
;

;==========================
; TRANSPORT CONFIGURATION
;==========================

; WebSocket transport for browser-based WebRTC connections
[transport-ws]
type=transport
protocol=ws
bind=0.0.0.0:8088
; Allow connections from your VoIP application network
local_net=***********/24
external_media_address=***********
external_signaling_address=***********

; UDP transport for traditional SIP clients (optional)
[transport-udp]
type=transport
protocol=udp
bind=0.0.0.0:5060
local_net=***********/24
external_media_address=***********
external_signaling_address=***********

; TCP transport (optional)
[transport-tcp]
type=transport
protocol=tcp
bind=0.0.0.0:5060

;==========================
; ENDPOINT TEMPLATES
;==========================

; Template for WebRTC endpoints
[webrtc_endpoint](!)
type=endpoint
context=default
disallow=all
allow=opus,ulaw,alaw,g722
webrtc=yes
use_ptime=yes
media_encryption=no
rtcp_mux=yes
ice_support=yes
media_use_received_transport=yes
transport=transport-ws

; Template for authentication
[webrtc_auth](!)
type=auth
auth_type=userpass

; Template for AOR (Address of Record)
[webrtc_aor](!)
type=aor
max_contacts=5
remove_existing=yes

;==========================
; USER EXTENSIONS
;==========================

; Extension 1000
[1000](webrtc_endpoint)
auth=1000
aors=1000

[1000](webrtc_auth)
password=password1000
username=1000

[1000](webrtc_aor)

; Extension 1001
[1001](webrtc_endpoint)
auth=1001
aors=1001

[1001](webrtc_auth)
password=password1001
username=1001

[1001](webrtc_aor)

; Extension 1002
[1002](webrtc_endpoint)
auth=1002
aors=1002

[1002](webrtc_auth)
password=password1002
username=1002

[1002](webrtc_aor)

; Extension 1003
[1003](webrtc_endpoint)
auth=1003
aors=1003

[1003](webrtc_auth)
password=password1003
username=1003

[1003](webrtc_aor)

; Extension 1004
[1004](webrtc_endpoint)
auth=1004
aors=1004

[1004](webrtc_auth)
password=password1004
username=1004

[1004](webrtc_aor)

; Extension 1005
[1005](webrtc_endpoint)
auth=1005
aors=1005

[1005](webrtc_auth)
password=password1005
username=1005

[1005](webrtc_aor)

;==========================
; GLOBAL SETTINGS
;==========================

[global]
type=global
max_forwards=70
user_agent=Asterisk VoIP Server
default_outbound_endpoint=default_outbound

; System settings
[system]
type=system
timer_t1=500
timer_b=32000
compact_headers=no
threadpool_initial_size=0
threadpool_auto_increment=5
threadpool_idle_timeout=60
threadpool_max_size=0
