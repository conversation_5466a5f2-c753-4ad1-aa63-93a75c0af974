# Server Configuration
PORT=8080
HOST=0.0.0.0

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRY_HOURS=24

# Database Configuration
DB_PATH=./voip.db

# Asterisk Configuration (use hostname instead of IP for better portability)
ASTERISK_HOST=asterisk.local
ASTERISK_AMI_PORT=5038
ASTERISK_AMI_USERNAME=admin
ASTERISK_AMI_SECRET=amp111

# SIP Configuration
SIP_DOMAIN=asterisk.local
SIP_PORT=8088

# Public host for frontend connections (auto-detected if not set)
PUBLIC_HOST=localhost

# Environment and service info
ENVIRONMENT=development
SERVICE_NAME=voip-backend

# CORS Configuration (auto-configured if not set)
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# Service discovery
DISCOVERY_MODE=auto

# Alternative configurations:
# For current IP setup: ASTERISK_HOST=***********, PUBLIC_HOST=***********
# For Docker: ASTERISK_HOST=voip-asterisk, PUBLIC_HOST=voip-backend
# For production: ASTERISK_HOST=sip.yourdomain.com, PUBLIC_HOST=api.yourdomain.com

# Debug Mode
DEBUG=true
