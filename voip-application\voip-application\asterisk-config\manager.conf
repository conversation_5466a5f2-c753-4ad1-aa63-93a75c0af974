;
; Asterisk Manager Interface (AMI) Configuration
; File: /etc/asterisk/manager.conf
;
; This configuration enables the AMI interface for your VoIP application
; to communicate with Asterisk for call management and monitoring
;

[general]
; Enable the manager interface
enabled = yes

; Port for AMI connections
port = 5038

; Bind to all interfaces to allow connections from your backend
bindaddr = 0.0.0.0

; Display manager connection activity
displayconnects = yes

; Timestamp format for manager events
timestampevents = yes

; Enable HTTP manager interface (optional, for web-based management)
webenabled = yes

; Allow multiple connections from the same IP
allowmultiplelogin = yes

; Block sockets for efficiency
block-sockets = no

; Debug level (0=off, 1=on)
debug = 0

; Maximum number of manager sessions
maxsessions = 100

;==========================
; MANAGER USERS
;==========================

; Admin user for your VoIP backend application
[admin]
; Password for AMI authentication
secret = amp111

; Permissions - read and write access to all AMI actions
read = all
write = all

; Deny specific actions (none in this case)
deny = 

; Allow specific actions (all in this case)
permit = 

; IP address restrictions (allow from your backend server)
; You can restrict this to your specific backend IP for security
permit = ***********/24
permit = 127.0.0.1/32
permit = localhost

; Event filtering (optional)
eventfilter = !Event: Newexten
eventfilter = !Event: VarSet
eventfilter = !Event: Newstate

; Additional user for monitoring (optional)
[monitor]
secret = monitor123
read = system,call,log,verbose,command,agent,user,config,command,dtmf,reporting,cdr,dialplan
write = system,call,log,verbose,command,agent,user,config,command,dtmf,reporting,cdr,dialplan
permit = ***********/24

; Read-only user for statistics (optional)
[readonly]
secret = readonly123
read = system,call,log,verbose,agent,user,config,dtmf,reporting,cdr,dialplan
write = 
permit = ***********/24

;==========================
; SECURITY SETTINGS
;==========================

; Enable encryption (optional, requires SSL setup)
;tlsenable = yes
;tlsbindaddr = 0.0.0.0:5039
;tlscertfile = /etc/asterisk/keys/asterisk.pem
;tlsprivatekey = /etc/asterisk/keys/asterisk.key

; Session timeout in seconds
authtimeout = 30

; Automatic logout timeout
autologoff = 3600

; Enable manager events
enabled = yes

; Channel event filtering
channelvars = 

; Hide sensitive information in events
hideconnect = no

; Broken pipe timeout
brokeneventsaction = yes
