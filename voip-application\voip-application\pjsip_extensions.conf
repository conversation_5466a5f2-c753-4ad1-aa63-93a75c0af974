# PJSIP Extensions Configuration for VoIP System
# Add these to your /etc/asterisk/pjsip.conf file

# Transport for WebSocket (if not already configured)
[transport-ws]
type=transport
protocol=ws
bind=0.0.0.0:8088

# Extensions 1001-1020

[1001]
type=endpoint
context=default
disallow=all
allow=ulaw,alaw
transport=transport-ws
auth=1001
aors=1001

[1001]
type=auth
auth_type=userpass
password=password1001
username=1001

[1001]
type=aor
max_contacts=5

[1002]
type=endpoint
context=default
disallow=all
allow=ulaw,alaw
transport=transport-ws
auth=1002
aors=1002

[1002]
type=auth
auth_type=userpass
password=password1002
username=1002

[1002]
type=aor
max_contacts=5

[1003]
type=endpoint
context=default
disallow=all
allow=ulaw,alaw
transport=transport-ws
auth=1003
aors=1003

[1003]
type=auth
auth_type=userpass
password=password1003
username=1003

[1003]
type=aor
max_contacts=5

[1004]
type=endpoint
context=default
disallow=all
allow=ulaw,alaw
transport=transport-ws
auth=1004
aors=1004

[1004]
type=auth
auth_type=userpass
password=password1004
username=1004

[1004]
type=aor
max_contacts=5

[1005]
type=endpoint
context=default
disallow=all
allow=ulaw,alaw
transport=transport-ws
auth=1005
aors=1005

[1005]
type=auth
auth_type=userpass
password=password1005
username=1005

[1005]
type=aor
max_contacts=5

[1006]
type=endpoint
context=default
disallow=all
allow=ulaw,alaw
transport=transport-ws
auth=1006
aors=1006

[1006]
type=auth
auth_type=userpass
password=password1006
username=1006

[1006]
type=aor
max_contacts=5

[1007]
type=endpoint
context=default
disallow=all
allow=ulaw,alaw
transport=transport-ws
auth=1007
aors=1007

[1007]
type=auth
auth_type=userpass
password=password1007
username=1007

[1007]
type=aor
max_contacts=5

[1008]
type=endpoint
context=default
disallow=all
allow=ulaw,alaw
transport=transport-ws
auth=1008
aors=1008

[1008]
type=auth
auth_type=userpass
password=password1008
username=1008

[1008]
type=aor
max_contacts=5

[1009]
type=endpoint
context=default
disallow=all
allow=ulaw,alaw
transport=transport-ws
auth=1009
aors=1009

[1009]
type=auth
auth_type=userpass
password=password1009
username=1009

[1009]
type=aor
max_contacts=5

[1010]
type=endpoint
context=default
disallow=all
allow=ulaw,alaw
transport=transport-ws
auth=1010
aors=1010

[1010]
type=auth
auth_type=userpass
password=password1010
username=1010

[1010]
type=aor
max_contacts=5

[1011]
type=endpoint
context=default
disallow=all
allow=ulaw,alaw
transport=transport-ws
auth=1011
aors=1011

[1011]
type=auth
auth_type=userpass
password=password1011
username=1011

[1011]
type=aor
max_contacts=5

[1012]
type=endpoint
context=default
disallow=all
allow=ulaw,alaw
transport=transport-ws
auth=1012
aors=1012

[1012]
type=auth
auth_type=userpass
password=password1012
username=1012

[1012]
type=aor
max_contacts=5

[1013]
type=endpoint
context=default
disallow=all
allow=ulaw,alaw
transport=transport-ws
auth=1013
aors=1013

[1013]
type=auth
auth_type=userpass
password=password1013
username=1013

[1013]
type=aor
max_contacts=5

[1014]
type=endpoint
context=default
disallow=all
allow=ulaw,alaw
transport=transport-ws
auth=1014
aors=1014

[1014]
type=auth
auth_type=userpass
password=password1014
username=1014

[1014]
type=aor
max_contacts=5

[1015]
type=endpoint
context=default
disallow=all
allow=ulaw,alaw
transport=transport-ws
auth=1015
aors=1015

[1015]
type=auth
auth_type=userpass
password=password1015
username=1015

[1015]
type=aor
max_contacts=5

[1016]
type=endpoint
context=default
disallow=all
allow=ulaw,alaw
transport=transport-ws
auth=1016
aors=1016

[1016]
type=auth
auth_type=userpass
password=password1016
username=1016

[1016]
type=aor
max_contacts=5

[1017]
type=endpoint
context=default
disallow=all
allow=ulaw,alaw
transport=transport-ws
auth=1017
aors=1017

[1017]
type=auth
auth_type=userpass
password=password1017
username=1017

[1017]
type=aor
max_contacts=5

[1018]
type=endpoint
context=default
disallow=all
allow=ulaw,alaw
transport=transport-ws
auth=1018
aors=1018

[1018]
type=auth
auth_type=userpass
password=password1018
username=1018

[1018]
type=aor
max_contacts=5

[1019]
type=endpoint
context=default
disallow=all
allow=ulaw,alaw
transport=transport-ws
auth=1019
aors=1019

[1019]
type=auth
auth_type=userpass
password=password1019
username=1019

[1019]
type=aor
max_contacts=5

[1020]
type=endpoint
context=default
disallow=all
allow=ulaw,alaw
transport=transport-ws
auth=1020
aors=1020

[1020]
type=auth
auth_type=userpass
password=password1020
username=1020

[1020]
type=aor
max_contacts=5
