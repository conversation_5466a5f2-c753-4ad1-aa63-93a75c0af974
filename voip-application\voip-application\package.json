{"name": "enterprise-voip-application", "version": "1.0.0", "description": "Enterprise VoIP Web Application - Professional voice communication solution", "authors": [{"name": "JOACHIM J. EUPHRASE", "email": "<EMAIL>", "role": "Frontend Lead Developer", "organization": "DevHub", "location": "Dar es Salaam, Tanzania"}, {"name": "KEVIN N. MRANDA", "email": "<EMAIL>", "role": "Backend Lead Developer", "organization": "DevHub", "location": "Dar es Salaam, Tanzania"}], "repository": {"type": "git", "url": "https://github.com/Euphrase8/voip-application.git"}, "license": "Apache-2.0", "private": false, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@headlessui/react": "^2.2.4", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.0.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.9.0", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "framer-motion": "^12.23.0", "jssip": "^3.10.1", "lucide-react": "^0.509.0", "node-fetch": "^3.3.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-router-dom": "^7.6.0", "react-scripts": "5.0.1", "sip.js": "^0.21.2", "tailwindcss": "^3.4.17", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "configure-asterisk": "node scripts/configure-asterisk.js configure", "discover-asterisk": "node scripts/configure-asterisk.js discover", "show-config": "node scripts/configure-asterisk.js show", "check-errors": "node scripts/fix-errors.js check", "fix-errors": "node scripts/fix-errors.js fix", "test-asterisk": "node scripts/test-asterisk-connection.js", "test-backend": "node scripts/test-backend-health.js", "update-status": "node scripts/update-system-status.js", "health-check": "npm run test-asterisk && npm run test-backend", "status": "node scripts/force-status-refresh.js", "test-health-service": "node scripts/test-system-health-service.js", "fix-admin": "node scripts/fix-admin-dashboard.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "autoprefixer": "^10.4.21", "postcss": "^8.5.6"}}