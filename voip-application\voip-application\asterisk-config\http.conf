;
; Asterisk HTTP Configuration for VoIP Application
; File: /etc/asterisk/http.conf
; 
; This configuration enables the HTTP interface required for WebSocket
; connections and web-based management of Asterisk
;

[general]
; Enable the HTTP server
enabled=yes

; Bind to all interfaces (allows connections from any IP)
bindaddr=0.0.0.0

; HTTP port (standard port for Asterisk HTTP interface)
bindport=8088

; Enable HTTPS if needed (optional)
;tlsenable=yes
;tlsbindaddr=0.0.0.0:8089
;tlscertfile=/etc/asterisk/keys/asterisk.pem
;tlsprivatekey=/etc/asterisk/keys/asterisk.key

; Session timeout in seconds
sessiontimeout=10000

; Enable static content serving
enablestatic=yes

; Redirect HTTP to HTTPS (if HTTPS is enabled)
;redirect=yes

; Session options
sessionlimit=100

; Enable Cross-Origin Resource Sharing (CORS) for web applications
; This is important for browser-based WebRTC connections
;
; Note: In production, you should restrict origins to your specific domains
; For development, you can use * to allow all origins

; Prefix for HTTP requests (IMPORTANT: Remove or set to empty for WebSocket)
; prefix=asterisk
; DISABLED: prefix causes WebSocket connection issues

; Enable server-sent events
;sslenable=yes

; Maximum number of HTTP sessions
;sessionlimit=100

; Enable WebSocket support (required for WebRTC)
; This is handled by the HTTP server and used by chan_pjsip
websocket_enabled=yes

; WebSocket write timeout (in milliseconds)
websocket_write_timeout=100

; Enable CORS support for WebRTC
[cors]
enabled=yes
origins=*
methods=GET,POST,PUT,DELETE,OPTIONS
headers=Content-Type,Authorization,X-Requested-With,Upgrade,Connection,Sec-WebSocket-Key,Sec-WebSocket-Version,Sec-WebSocket-Protocol
